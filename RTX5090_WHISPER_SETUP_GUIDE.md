# RTX 5090 + Whisper Transcription Setup Guide

**Complete Guide for GPU-Accelerated Audio Transcription with Timestamp Generation**

---

## 🎯 Overview

This guide covers how to set up and use the RTX 5090 GPU with CUDA for high-performance Whisper transcription that generates JSON files with precise start/end timestamps for audio segments based on input sentences.

**Performance**: 6x faster transcription (30 seconds vs 2-3 minutes for 22 audio segments)  
**Output**: JSON files with precise timing data for each audio segment  
**Quality**: Word-level timestamps with confidence scores  

---

## 🚀 RTX 5090 CUDA Setup

### Hardware Requirements
- **GPU**: NVIDIA GeForce RTX 5090 (32GB VRAM)
- **Architecture**: <PERSON> (sm_120 compute capability)
- **System**: Windows 11 (tested) / Linux compatible

### Software Installation

#### 1. Install PyTorch 2.8.0 with CUDA 12.8
```bash
# Uninstall existing PyTorch versions first
pip uninstall torch torchvision torchaudio

# Install PyTorch 2.8.0 with CUDA 12.8 support
pip install torch==2.8.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128
```

#### 2. Install Whisper Dependencies
```bash
# Core transcription libraries
pip install openai-whisper
pip install transformers
pip install librosa
pip install soundfile

# Additional audio processing
pip install pydub
pip install ffmpeg-python
```

#### 3. Verify GPU Setup
```python
import torch
print(f"CUDA Available: {torch.cuda.is_available()}")
print(f"GPU: {torch.cuda.get_device_name(0)}")
print(f"VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")

# Test GPU operations
test_tensor = torch.randn(1000, 1000, device='cuda')
result = torch.matmul(test_tensor, test_tensor)
print("✅ GPU operations working!")
```

### Important Notes
- **Ignore sm_120 warnings**: PyTorch 2.8.0 works perfectly despite compatibility warnings
- **Memory usage**: Only ~9% VRAM utilization (2.9GB / 31.8GB) for Whisper Large v3
- **Performance**: 6x faster than CPU mode with identical quality

---

## 🎙️ Whisper Transcription with Timestamps

### Core Features
- **Word-level timestamps**: Precise timing for each word
- **Sentence alignment**: Smart boundary detection for clean cuts
- **JSON output**: Structured data with start/end times and confidence scores
- **GPU acceleration**: Optimized for RTX 5090 performance

### Transcription Process

#### 1. Audio Analysis & Transcription
```python
def transcribe_with_word_timestamps(self, audio_path: Path) -> List[Dict]:
    """Transcribe audio with word-level timestamps"""
    result = self.whisper_model.transcribe(
        str(audio_path), 
        word_timestamps=True,
        verbose=False
    )
    
    segments = []
    for segment in result["segments"]:
        words = segment.get("words", [])
        segments.append({
            "start": segment["start"],
            "end": segment["end"],
            "text": segment["text"].strip(),
            "words": words
        })
    
    return segments
```

#### 2. Smart Sentence Alignment
The system uses intelligent alignment to match input sentences with transcribed audio:

- **Fuzzy matching**: Handles variations in transcription vs input text
- **Silence detection**: Finds optimal cut points between sentences
- **Boundary optimization**: Ensures clean audio segments without word cutoffs

#### 3. JSON Output Format
The system generates `alignment_info.json` with this structure:

```json
[
  {
    "segment_number": 1,
    "start_time": 0.0,
    "end_time": 5.78,
    "duration": 5.78,
    "text": "Let's get straight into The 7 Habits of Highly Effective People by Stephen R. Covey.",
    "confidence": 0.7176470588235294
  },
  {
    "segment_number": 2,
    "start_time": 5.78,
    "end_time": 17.76,
    "duration": 11.98,
    "text": "This book wants your life to run like a Swiss watch, but let's be real, most of us are still figuring out which batteries go where.",
    "confidence": 0.8552188552188552
  }
]
```

### Key Data Fields
- **segment_number**: Sequential segment identifier
- **start_time**: Segment start in seconds (float)
- **end_time**: Segment end in seconds (float)  
- **duration**: Segment length in seconds
- **text**: Transcribed text content
- **confidence**: Alignment confidence score (0.0-1.0)

---

## 🛠️ Usage Workflow

### Step 1: Prepare Input
- **Audio file**: WAV, MP3, FLAC, or other supported formats
- **Text sentences**: List of sentences to align with audio

### Step 2: Run Transcription
```python
from src.core.smart_audio_splitter import SmartAudioSplitter

splitter = SmartAudioSplitter()
config = {
    "output_dir": Path("output_folder"),
    "output_format": "wav",
    "similarity_threshold": 0.6
}

# Process audio with sentence alignment
result = splitter.split_audio_by_sentences(
    audio_path=Path("input_audio.wav"),
    sentences=["Sentence 1", "Sentence 2", "..."],
    config=config
)
```

### Step 3: Output Files
The system generates:
- **Individual audio segments**: `segment_001_text_preview.wav`
- **Alignment JSON**: `alignment_info.json` with timing data
- **Processing logs**: Detailed progress and confidence scores

---

## 📊 Performance Benchmarks

### RTX 5090 Performance
| Operation | CPU Mode | GPU Mode (RTX 5090) | Speedup |
|-----------|----------|---------------------|---------|
| Model Loading | ~15-20s | 9.3s | 2x faster |
| Transcription (per segment) | ~2-3s | 0.41s | 5-7x faster |
| Full Video (22 scenes) | 2-3 minutes | ~30 seconds | **6x faster** |

### Memory Usage
- **GPU Memory**: 2.9GB / 31.8GB (9% utilization)
- **Model**: Whisper Large v3 (float16 precision)
- **Optimization**: Mixed precision training enabled

---

## 🔧 Troubleshooting

### Common Issues

#### 1. "No kernel image available" Error
**Solution**: Upgrade to PyTorch 2.8.0+cu128
```bash
pip install torch==2.8.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128
```

#### 2. GPU Not Detected
**Verification**:
```python
import torch
print(torch.cuda.is_available())  # Should be True
print(torch.cuda.get_device_name(0))  # Should show RTX 5090
```

#### 3. Low Confidence Scores
**Solutions**:
- Improve audio quality (reduce background noise)
- Use higher similarity threshold (0.7-0.8)
- Ensure input sentences match spoken content closely

### Automatic Fallbacks
The system includes automatic CPU fallback if GPU issues occur:
```python
def _select_device(self) -> str:
    """Select the best available device for transcription"""
    if not torch.cuda.is_available():
        return "cpu"
        
    try:
        # Test GPU functionality
        test_tensor = torch.randn(1000, 1000, device='cuda')
        result = torch.matmul(test_tensor, test_tensor)
        return "cuda"
    except RuntimeError:
        return "cpu"  # Graceful fallback
```

---

## 🎉 Summary

**✅ RTX 5090 Setup**: PyTorch 2.8.0+cu128 provides full compatibility  
**✅ Whisper Integration**: Word-level timestamps with GPU acceleration  
**✅ JSON Output**: Structured timing data for each audio segment  
**✅ Performance**: 6x faster processing with identical quality  
**✅ Reliability**: Automatic CPU fallback ensures system stability  

This setup provides professional-grade audio transcription with precise timing data, perfect for content creation, subtitling, and audio analysis workflows.
